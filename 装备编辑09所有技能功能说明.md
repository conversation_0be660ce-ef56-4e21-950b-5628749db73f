# 装备编辑 09*所有 技能功能实现说明

## 概述
为玉帝后台版的装备编辑功能添加了 `09*所有` 指令，可以一次性添加所有对应装备类型的技能。

## 修改的文件

### 1. 装备重铸界面编辑功能
**文件**: `scripts玉帝后台版/UI/forging/equipRemake/EquipRemakeBoard.as`

**主要修改**:
- 修改了 `EquipEdit_Equip` 函数中的技能添加逻辑
- 添加了 `getAllEquipSkills` 方法
- 更新了帮助文本，添加 `09*所有` 的说明

### 2. 右键菜单装备编辑功能
**文件**: `scripts玉帝后台版/UI/base/btnList/BtnList.as`

**主要修改**:
- 修改了 `EquipEdit` 函数中的技能添加逻辑
- 添加了 `getAllEquipSkills` 方法

## 功能详情

### 支持的指令
- **09*技能代码** - 添加单个技能（原有功能）
- **09*所有** - 添加所有装备技能（新功能）
- **09*all** - 添加所有装备技能（英文版本）

### 技能分类

#### 头盔和腰带技能 (headSkill)
当装备类型为 `head` 或 `belt` 时，添加以下技能：
- `godHand_equip` - 上帝的护佑
- `immune_equip` - 免疫
- `magneticField_equip` - 磁力场
- `strongHalo_equip` - 顽强光环
- `murderous_equip` - 嗜爪之怒
- `poisonRange_equip` - 瘴气
- `attackSpeedHalo_equip` - 耐久光环

#### 战衣和裤子技能 (coatSkill)
当装备类型为 `coat` 或 `pants` 时，添加以下技能：
- `sacrifice_equip` - 牺牲
- `backStrong_equip` - 钢背
- `anionSkin_equip` - 负离子外壳
- `treater_equip` - 净化器
- `backWeak_equip` - 芒刺
- `thornSkin_equip` - 荆棘外表
- `refraction_equip` - 折射

#### 时装技能 (fashionSkill)
当装备类型为 `fashion` 时，添加以下技能：
- `summonWolf_bigBoss` - 召唤群狼
- `zoomOut` - 装甲压制

## 使用方法

### 方法1: 装备重铸界面
1. 进入锻造界面 → 装备 → 装备重铸
2. 选择要编辑的装备
3. 点击编辑按钮
4. 在弹出的输入框中输入 `09*所有`
5. 系统会自动添加所有对应类型的装备技能

### 方法2: 右键菜单编辑
1. 在背包或装备界面右键点击装备
2. 选择"装备编辑"选项
3. 在弹出的输入框中输入 `09*所有`
4. 系统会自动添加所有对应类型的装备技能

## 技能效果说明

### 头盔/腰带技能效果
- **上帝的护佑**: 受到致命伤害时进入无敌状态
- **免疫**: 有概率免疫敌人技能效果
- **磁力场**: 定期释放磁力场，使敌方子弹偏离
- **顽强光环**: 为周围友方单位增加防御力
- **嗜爪之怒**: 受攻击时有概率增加攻击力
- **瘴气**: 使用技能时释放毒雾
- **耐久光环**: 为周围友方单位增加攻击速度

### 战衣/裤子技能效果
- **牺牲**: 受到伤害时为周围友方单位回复生命值
- **钢背**: 减少来自背面的伤害
- **负离子外壳**: 被攻击时有概率麻痹攻击者
- **净化器**: 定期清除自身负面状态
- **芒刺**: 攻击敌人背部造成额外伤害
- **荆棘外表**: 被攻击时有概率眩晕攻击者
- **折射**: 被攻击时有概率不受伤害并反弹伤害

### 时装技能效果
- **召唤群狼**: 挥舞屠夫时召唤尸狼灵魂攻击
- **装甲压制**: 骑乘载具时定期投掷玩具坦克

## 注意事项

1. **技能叠加**: 使用 `09*所有` 会将技能添加到现有技能列表中，不会清除原有技能
2. **装备类型**: 系统会根据装备的 `partType` 自动判断应该添加哪类技能
3. **成功提示**: 添加成功后会显示添加的技能数量
4. **兼容性**: 新功能与原有的单个技能添加功能完全兼容

## 示例用法

### 单独使用
```
09*所有
```

### 与其他指令组合使用
```
等级*99&品质*red&09*所有
```

这个指令会同时设置装备等级为99、品质为红色，并添加所有装备技能。

## 技术实现

### 核心逻辑
```actionscript
if(ArrNow[1] == "所有" || ArrNow[1] == "all")
{
   // 添加所有装备技能
   var allSkillArr:Array = this.getAllEquipSkills(s0.partType);
   s0.skillArr = s0.skillArr.concat(allSkillArr);
   Gaming.uiGroup.alertBox.showSuccess("已添加所有装备技能，共" + allSkillArr.length + "个技能！");
}
```

### 技能获取方法
```actionscript
private function getAllEquipSkills(partType:String) : Array
{
   // 根据装备类型返回对应的技能数组
   if(partType == "head" || partType == "belt") {
      return headSkills;
   } else if(partType == "fashion") {
      return fashionSkills;
   } else {
      return coatSkills;
   }
}
```

这个功能大大简化了装备技能的添加过程，特别适合测试和快速配置装备。
