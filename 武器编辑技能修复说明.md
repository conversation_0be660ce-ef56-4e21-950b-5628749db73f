# 武器编辑技能修复说明

## 问题描述
原本的武器编辑功能中，"武器技能"选项包含了游戏中所有的技能，包括很多对武器无用的技能，如：
- 敌人专用技能
- 宠物技能
- 装备技能
- 载具技能
- 状态技能等

这导致武器编辑时会添加很多无效技能，影响游戏体验。

## 修复内容

### 修改的文件
**文件**: `scripts玉帝后台版/UI/base/btnList/BtnList.as`

### 主要修改

#### 1. 新增功能指令
- **26*所有** - 添加所有普通武器技能（24个）
- **26*神级所有** - 添加所有神级武器技能（17个）
- **26*all** - 添加所有普通武器技能（英文版）
- **26*godall** - 添加所有神级武器技能（英文版）
- **26*清空** - 清空所有武器技能

#### 2. 修复原有功能
- 完全替换了原有的741个技能列表
- 现在单个技能添加只支持真正的武器技能
- 添加了技能验证，无效技能会显示错误提示

#### 2. 技能分类

##### 普通武器技能 (24个)
```
Hit_SlowMove_ArmsSkill      - 击中减速
Kill_AddCharger_ArmsSkill   - 击毙补充弹药
Kill_AddLifeMul_ArmsSkill   - 击毙回复
Hit_Poison_ArmsSkill        - 剧毒
Kill_Spurting_ArmsSkill     - 击毙溅射
Kill_Crazy_ArmsSkill        - 振奋
Hit_Spurting_ArmsSkill      - 击中溅射
Hit_disabled_ArmsSkill      - 致残
Hit_Paralysis_ArmsSkill     - 击中麻痹
Hit_blindness_ArmsSkill     - 致盲
Hit_AddLifeMul_ArmsSkill    - 击中回复
Hit_silence_ArmsSkill       - 击中沉默
Hit_Paralysis_ArmsSkill2    - 超级麻痹
Hit_burn_ArmsSkill          - 炎爆
cold_ArmsSkill              - 冷凝
erosion_ArmsSkill           - 蚀骨
beatBack_ArmsSkill          - 痛击
combo_ArmsSkill             - 连弩
viscous_ArmsSkill           - 粘性
viscousSuper_ArmsSkill      - 超级粘性
shotgunBlade_ArmsSkill      - 跳斩
godMace_ArmsSkill           - 上帝之杖
windThunder_ArmsSkill       - 风雷
lash_ArmsSkill              - 突袭
beadCrossbow_ArmsSkill      - 子母弹
```

##### 神级武器技能 (17个)
```
Hit_hitMissile_godArmsSkill    - 击中派生
Hit_seckill_godArmsSkill       - 瞬秒4
Hit_finalkill_godArmsSkill     - 绝灭
Hit_crazy_godArmsSkill         - 快感
Hit_atry_godArmsSkill          - 搏命
Hit_posion7_godArmsSkill       - 七步毒
Hit_imploding_godArmsSkill     - 爆石
Hit_pointBoom_godArmsSkill     - 引爆
Hit_fleshSkill_godArmsSkill    - 刷新
Hit_Lightning_godArmsSkill     - 击中闪电
Hit_Hammer_godArmsSkill        - 击中眩晕
Hit_SuperMissile_godArmsSkill  - 超级派生
laserKill_godArmsSkill         - 影灭
imploding_blackArmsSkill       - 爆沙
fear_godArmsSkill              - 爆胆
fear_godArmsSkill2             - 爆震
sickle_godArmsSkill            - 破甲
sickle_godArmsSkill2           - 溃甲
```

## 使用方法

### 访问方式
1. 在背包或装备界面右键点击武器
2. 选择"武器编辑"选项
3. 在弹出的输入框中输入指令

### 指令示例

#### 添加所有普通武器技能
```
26*所有
```

#### 添加所有神级武器技能
```
26*神级所有
```

#### 组合使用
```
武器名字*神器&武器品质*红&26*所有&26*神级所有
```

## 技能效果说明

### 普通武器技能效果
- **击中减速**: 攻击敌人时有概率使其减速
- **击毙补充弹药**: 击杀敌人时补充弹药
- **击毙回复**: 击杀敌人时回复生命值
- **剧毒**: 攻击时有概率使敌人中毒
- **击毙溅射**: 击杀敌人时产生溅射伤害
- **振奋**: 击杀敌人时提升攻击力
- **击中溅射**: 攻击时产生溅射伤害
- **致残**: 攻击时有概率使敌人致残
- **击中麻痹**: 攻击时有概率使敌人麻痹
- **致盲**: 攻击时有概率使敌人失明
- **击中回复**: 攻击敌人时回复生命值
- **击中沉默**: 攻击时有概率使敌人沉默
- **超级麻痹**: 强化版麻痹效果
- **炎爆**: 攻击时产生火焰爆炸
- **冷凝**: 攻击时产生冰冻效果
- **蚀骨**: 攻击时产生腐蚀效果
- **痛击**: 增加暴击伤害
- **连弩**: 连续射击效果
- **粘性**: 子弹具有粘性效果
- **超级粘性**: 强化版粘性效果
- **跳斩**: 近战跳跃攻击
- **上帝之杖**: 神圣攻击效果
- **风雷**: 风雷攻击效果
- **突袭**: 快速突进攻击
- **子母弹**: 分裂弹药效果

### 神级武器技能效果
- **击中派生**: 攻击时产生追踪导弹
- **瞬秒4**: 有概率瞬间击杀敌人
- **绝灭**: 超强瞬杀效果
- **快感**: 击杀敌人时获得快感状态
- **搏命**: 生命值越低攻击力越高
- **七步毒**: 剧毒效果，七步内必死
- **爆石**: 攻击时产生爆炸碎石
- **引爆**: 延时爆炸效果
- **刷新**: 重置技能冷却时间
- **击中闪电**: 攻击时产生闪电
- **击中眩晕**: 攻击时使敌人眩晕
- **超级派生**: 强化版派生导弹
- **影灭**: 影子攻击效果
- **爆沙**: 沙尘爆炸效果
- **爆胆**: 恐惧爆炸效果
- **爆震**: 震荡爆炸效果
- **破甲**: 无视敌人护甲
- **溃甲**: 强化版破甲效果

## 修复效果

### 修复前问题
- 武器技能列表包含500+个技能
- 大部分技能对武器无效
- 添加无用技能影响游戏平衡

### 修复后改进
- 只包含真正的武器技能
- 普通技能25个，神级技能18个
- 所有技能都对武器有效
- 提供批量添加功能
- 保持原有单个技能添加功能

## 技术实现

### 核心逻辑
```actionscript
if(ArrNow[1] == "所有" || ArrNow[1] == "all")
{
   // 添加所有普通武器技能
   var allArmsSkills:Array = this.getAllArmsSkills();
   s0.skillArr = s0.skillArr.concat(allArmsSkills);
   Gaming.uiGroup.alertBox.showSuccess("已添加所有武器技能，共" + allArmsSkills.length + "个技能！");
}
else if(ArrNow[1] == "神级所有" || ArrNow[1] == "godall")
{
   // 添加所有神级武器技能
   var allGodArmsSkills:Array = this.getAllGodArmsSkills();
   s0.godSkillArr = s0.godSkillArr.concat(allGodArmsSkills);
   Gaming.uiGroup.alertBox.showSuccess("已添加所有神级武器技能，共" + allGodArmsSkills.length + "个技能！");
}
```

### 技能获取方法
- `getAllArmsSkills()`: 返回所有普通武器技能数组
- `getAllGodArmsSkills()`: 返回所有神级武器技能数组

## 注意事项

1. **技能叠加**: 新功能会将技能添加到现有技能列表中，不会清除原有技能
2. **技能分类**: 普通技能和神级技能分别存储在不同的数组中
3. **兼容性**: 保持与原有单个技能添加功能的完全兼容
4. **成功提示**: 添加成功后会显示添加的技能数量

这个修复大大改善了武器编辑的体验，确保只添加真正有用的武器技能！
