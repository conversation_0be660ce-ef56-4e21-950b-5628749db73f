package UI.forging.armsRemake
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.must.NormalMustBox;
   import UI.forging.armsUpgrade.ArmsUpgradeBoard;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.arms.ArmsData;
   import dataAll.arms.ArmsDataGroup;
   import dataAll.arms.creator.ArmsRemakeCtrl;
   import dataAll.arms.creator.ArmsSpecialAndSkill;
   import dataAll.arms.save.ArmsSave;
   import dataAll.items.creator.OneProData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.skill.define.SkillDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.system.System;
   import flash.text.TextField;
   
   public class ArmsRemakeBoard extends NormalUI
   {
      
      private var copyData:ArmsData;
      
      public var armsUpgradeBoard:ArmsUpgradeBoard;
      
      private var proTag:Sprite;
      
      private var mustSp:Sprite;
      
      private var itemsGripSp:MovieClip;
      
      private var btnSp:MovieClip;
      
      private var gotoBackSp:MovieClip;
      
      private var proTxt:TextField;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var gotoBackBtn:NormalBtn = new NormalBtn();
      
      private var itemsGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      private var proBox:ItemsGripBox = new ItemsGripBox();
      
      private var _nowData:ArmsData;
      
      private var nowProDataArr:Array = [];
      
      private var tempRemakeSave:ArmsSave;
      
      private var beforeSave:ArmsSave;
      
      public function ArmsRemakeBoard()
      {
         super();
      }
      
      public function set nowData(da0:ArmsData) : void
      {
         if(da0 != this._nowData || !da0)
         {
            this.nowProDataArr = [];
            this.beforeSave = null;
         }
         this.armsUpgradeBoard.nowData = da0;
         this._nowData = da0;
      }
      
      public function get nowData() : ArmsData
      {
         return this.armsUpgradeBoard.nowData;
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["proTag","mustSp","btnSp","gotoBackSp","itemsGripSp","proTxt"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("");
         addChild(this.gotoBackBtn);
         this.gotoBackBtn.setImg(this.gotoBackSp);
         this.gotoBackBtn.setName("编辑属性");
         addChild(this.itemsGrip);
         this.itemsGrip.setImg(this.itemsGripSp);
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.itemsGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustSp);
         addChild(this.proBox);
         NormalUICtrl.setTag(this.proBox,this.proTag);
         this.proBox.arg.init(1,8,0,4);
         this.proBox.evt.setWantEvent(true,false,false,true,true);
         this.proBox.setIconPro("ForgingUI/armsProBar",50,50);
         this.proBox.addEventListener(ClickEvent.ON_CLICK,this.proBarClick);
         this.proBox.addEventListener(ClickEvent.ON_OVER,this.proBarOver);
         this.proBox.addEventListener(ClickEvent.ON_OUT,this.proBarOut);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.btn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.btn.addEventListener(MouseEvent.MOUSE_OUT,this.proBarOut);
         this.gotoBackBtn.addEventListener(MouseEvent.CLICK,this.gotoBackBtnClick);
         this.gotoBackBtn.addEventListener(MouseEvent.MOUSE_OVER,this.btnOver);
         this.gotoBackBtn.addEventListener(MouseEvent.MOUSE_OUT,this.proBarOut);
         this.showNone();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"arms");
         this.showOneArmsDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function armsGripClick(da0:ArmsData) : void
      {
         if(visible)
         {
            this.showOneArmsDataAndPan(da0);
         }
      }
      
      private function proBarClick(e:ClickEvent) : void
      {
         var grip0:ItemsGrid = e.child as ItemsGrid;
         var da0:OneProData = e.childData as OneProData;
         if(!da0.noChangeLockB)
         {
            da0.lockB = !da0.lockB;
         }
         grip0.inData_proData(da0);
         this.fleshMust();
      }
      
      private function proBarOver(e:ClickEvent) : void
      {
         var d0:SkillDefine = null;
         var str0:String = null;
         this.proBarOut(e);
         var da0:OneProData = e.childData as OneProData;
         if(da0.type == "skill")
         {
            d0 = Gaming.defineGroup.skill.getDefine(da0.name);
            str0 = ComMethod.color("<b>" + d0.cnName + "</b>","#FFFF00");
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0 + "\n" + d0.description);
         }
      }
      
      private function proBarOut(e:Event) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function btnOver(e:Event) : void
      {
         var str0:String = "";
         if(e.target == this.btn)
         {
            str0 = "点击即可复制当前武器";
         }
         else if(e.target == this.gotoBackBtn)
         {
            str0 = "如果要编辑多条属性，则用&符号连接，比如 01*888&02*666";
         }
         if(str0 == "")
         {
            this.proBarOut(e);
         }
         else
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      private function showOneArmsDataAndPan(da0:ArmsData, fleshNowProDataArrB0:Boolean = false) : void
      {
         var dg0:ArmsDataGroup = null;
         this.btn.setName("复制武器");
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要复制的武器。");
         if(this.copyData != null)
         {
            this.btn.actived = true;
            this.btn.setName("添加武器");
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
         if(da0)
         {
            dg0 = Gaming.PG.da.findArmsData(da0);
            if(dg0 is ArmsDataGroup)
            {
               this.showOneArmsData(da0,fleshNowProDataArrB0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneArmsData(da0:ArmsData, fleshNowProDataArrB0:Boolean = false) : void
      {
         this.nowData = da0;
         this.itemsGrip.inData_arms(da0);
         if(fleshNowProDataArrB0 || this.nowProDataArr.length == 0)
         {
            this.nowProDataArr = ArmsSpecialAndSkill.getOneProDataArr(da0.save);
         }
         this.proBox.inData_byArr(this.nowProDataArr,"inData_proData");
         this.proTxt.text = this.copyData == null ? "点击添加武器即可添加刚刚复制的武器到当前背包" : "点击复制武器即可复制当前武器";
         this.fleshMust();
      }
      
      private function fleshMust() : void
      {
         var must_d0:MustDefine = ArmsRemakeCtrl.getMust(this.nowData.save,this.nowProDataArr);
         var mustB0:Boolean = this.mustBox.inData(must_d0);
         var lockAllB0:Boolean = this.proIsLockAllB();
         this.btn.actived = true;
         this.gotoBackBtn.visible = true;
      }
      
      private function proIsLockAllB() : Boolean
      {
         var da0:* = null;
         for each(da0 in this.nowProDataArr)
         {
            if(!da0.lockB)
            {
               return false;
            }
         }
         return true;
      }
      
      private function showNone() : void
      {
         this.nowData = null;
         this.beforeSave = null;
         this.nowProDataArr = [];
         this.tempRemakeSave = null;
         this.itemsGrip.clearData();
         this.proBox.inData_byArr([],"inData_proData");
         this.mustBox.setShowState(false);
         this.btn.actived = true;
         this.gotoBackBtn.visible = true;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var s0:ArmsSave = new ArmsSave();
         if(this.copyData == null)
         {
            this.copyData = this.nowData;
            Gaming.uiGroup.alertBox.showSuccess("复制当前武器数据成功！");
            this.showOneArmsDataAndPan(this.nowData);
         }
         else
         {
            s0.inData_byObj(this.copyData.save);
            Gaming.PG.da.armsBag.addSave(s0);
            Gaming.uiGroup.alertBox.showSuccess("添加武器成功！");
            this.copyData = null;
            this.showOneArmsDataAndPan(this.nowData);
         }
      }
      
      private function gotoBackBtnClick(e:MouseEvent) : void
      {
         if(this.nowData)
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("武器名字[00*名字]武器品质[01*颜色]\n武器类型[02*类型]武器弹容[03*数量]\n武器单发[04*数量]武器等级[05*数量]\n武器射速[06*数量]|下一页[07*数量]|","",this.ArmsEdit);
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("武器数据不存在！");
         }
      }
      
      private function ArmsEdit(str0:String) : void
      {
         var i:int = 0;
         var s0:ArmsSave = new ArmsSave();
         var ArrNum:Array = new Array();
         var ArrNow:Array = new Array();
         var _ArrColor0:* = ["earthquake","earthquake_link","endlessRocket","lightningTower","energyShield","electricDevicer","electricDevicerClearKing","lightningTower_die","lightningTower_lightning","shuttleDevicer","blackHoleDevicer","dinosaurEgg","squibDevice","terroristBox","knightsMedal","skeletonMedal","skeletonWand","skeletonWandHit","terroristBox_screaming","squibDevice_screaming","outfit_follow","outfit_blood","outfit_shootMissile","outfit_jump","outfit_crit","outfit_boom","outfit_wolong","outfit_wolong_hurt","outfit_eagle","outfit_elephant","nuclear_peak","gaze_peak","sacrifice_peak","invisibilityEver","upperLimitSecond","upperLimitSecond2","noSkillHurt","noBulletHurt","onlyWeaponHurt","onlyWeaponHurtBattle","onlyWeaponHurtSet","onlyUnderCrossbow","onlyUnderOldRocket","State_SpellImmunity","State_Invincible","State_InvincibleThrough","State_noAiFind","State_noAllSkill","noAttackOrder","noMoveSpeed","noArmsShoot","killMeTimeOver","aiExcape","sumBossAtten","sumBossAtten2","sumBossAtten3"
         ,"offBossAtten","metalCorrosion","State_lowMove","State_AddMove","State_AddMove50","jumpNumAdd1","jumpNumAddZero","standImageByLife","UnderRos_AddMove_Battle","boom_headless","suicide_headless","cmldef_enemy","cmldef2_enemy","cmldef3_enemy","Hit_Crit_Fat","circle_inward_shell","sweep_shell","Doctor2_cloned","FightKing_disabled","crazy_king","hyperopia_incapable","through_enemy","noBulletReduct","throughClose","sweep_runAway","selfBurn_task","hammer_hit","choppedAttack_FightShooter","windAttack_FightShooter","FightKing_hitParalysis","FightKing_crazy","SmallSpider_hitParalysis","suckBlood_enemy","gaze_enemy","despise_enemy","fastForward_enemy","hurtDefence_enemy","suicide_treasure","rigidBody_enemy","defenceBounce_enemy","noBounce_enemy","roll_hugePosion","posion7_hugePosion","corrosion_hugePosion","silence_hugePosion","hiding_hugePosion","splash_HugePoison","summonedGasBom_HugePoison","suicide_GasBomb","selfBoom_GasBomb","slowMove_GasBomb","noUnderHurt5_GasBomb","killCharm","screaming_enemy"
         ,"changeToZombie_enemy","skillCopy_enemy","invisibility_enemy","knife_skeleton","knife_skeleton2","atry_skeleton","blindness_skeleton","teleport_skeleton","crazy_skeleton","hammer_enemy","hammer_enemy_link","knife_skeleton_hammer","ironBody_enemy","magneticField_enemy","magneticField_enemy_link","beatBack_iron","silence_wind","emp_wind","slowMove_wind","bar_wind","wizardAnger_wind","wizardAnger_wind_blood","poisonClaw_wind","noSR","xxx_wind","winding_mummy","noSpeedReduce","crazy_knights","hammer_knights","treater_knights","trample_knights","boundless_enemy","boundless_enemy_link","boundless_enemy_pass","groupCrazy_enemy","lifeLink_wolf","feeding_wolf","groupReverseHurt_enemy","posion7_wolf","summon_FightWolf","anger_FightWolf","hammer_FightWolf","laser_FightWolf","treater_FightWolf","NuggetsShoot","wind_Nuggets","likeMissle_Shapers","likeMissleNo","likeMissleNo2","missile_Sentry","addMove_Crawler","summonedSpider_Crawler","Crawler_cloned","crazy_enemy","groupLight_enemy","groupSpeedUp_enemy"
         ,"teleport_enemy","hiding_enemy","hidingAll_enemy","feedback_enemy","imploding_enemy","paralysis_enemy","pointBoom_enemy","sweep_enemy","moreMissile_enemy","liveReplace_enemy","silence_enemy","globalSpurting_enemy","murderous_enemy","poisonClaw_enemy","selfBurn_enemy","bullying_enemy","skillGift_enemy","disabled_enemy","slowMove_enemy","strong_enemy","slowMoveHalo_enemy","disabledHalo_enemy","trueshot_enemy","corrosion_enemy","desertedHalo_enemy","rebirth_enemy","recovery_enemy","recoveryHalo_enemy","tenacious_enemy","reverseHurt_enemy","teleport_enemy15","backHurt_enemy_link","paralysis_enemy_link","lifeReplace_enemy_link","skillCopy_enemy_link","globalSpurting_enemy_link","slowMoveNoClear","onlyUnderMiningSpade","onlyUnderMiningShovels","onlyUnderChristmasGun","noEleUnder","lifeBarShowCd","orcCreateThings","dedicationLove","resistPerLove2","defenceAddLove","vehicleAddLove30","resistPerLoveAdd2","underInvincibleHurtLove","nearAddLifeLove","mainResistPerLove","summonedSpider_spiderKing"
         ,"feeding_spider","pounce_spider","electricBoom_enemy","invisibility_SpiderKing","acidRain_SpiderKing","summonedSpider_spiderKing_extra","DropEffect_AddLifeMul","DropEffect_AddChargerMul","invincibleDrugDrop","DropEffect_AddMoveSpeedMul","addMocha","godHiding_things","godHiding_Pet","moonCake","tangyuan","jiaozi","armsDropDouble","equipDropDouble","armsDropDoubleAndGem","equipDropDoubleAndEquipGem","highPetCard","highVehicleCard","highCardState","electromagnet","superSpreadCard","skeletonCard","skeletonCard_link","pumpkinHead","pumpkinHead","wolfFashionSkill","goldFalcon","dragonHeadSkill","crazy_sanji","xiaoBoShoot","xiaoMingShoot","seckillNormalEnemy","xiaoAiShoot","shotgunBladeHero","chinaCaptainSkill","armyCommanderSkill","bladeSkill","cyanArmySkill","bioShockSkill","hurtBossAdd","highDrill","superHighDrill","highDrillHit","extendCd","arenaHurtAdd","Hit_SlowMove","Range_SlowMove","Manual_AddLife","BackHurt","pumpkinDropEffect","gmMask1","gmMask2","gmMask3","superMoreBullet","superSkillGift"
         ,"maxSpeedTask","madmanHead","betHit","killAll","attackNoDodge","findHide","invincibleZombieEnemyHit","sniperKingBuff","sniperKingEnemyHit","sniperKingEnemyUnder","sniperKingEnemyUnder2","bulletRainBallHit","flySkyBatBuff","rifleSensitive","sniperSensitive","shotgunSensitive","pistolSensitive","rocketSensitive","crossbowSensitive","flamerSensitive","laserSensitive","otherSensitive","weaponSensitive","vehicleSensitive","petSensitive","redArmsSensitive","handSensitive","wizardAngerEdit","huntParts","acidicParts","whirlwind_FightKing","shake_FightKing","shoot_FightKing","hyperopia_pet","shelling_ZombieKing","helmet_PetZombieFootball","shoot_PetZombieFootball","globalLight_PetZombieFootball","shoot2_PetTyphoonWitch","wind_PetTyphoonWitch","wizardAnger_PetTyphoonWitch","sacrifice_PetIronChief","defenceAuras_PetIronChief","godHand_PetIronChief","endlessBombing_skull","current_skull","degradation_PetBoomSkull","summon_PetFightWolf","laser_PetFightWolf","treater_PetFightWolf","anger_PetFightWolf"
         ,"charged_PetLake","lightBall_PetLake","static_PetLake","agile_PetLake","blindness_anger_PetFightWolf","lightBall_PetLake_slow","dizziness_anger_PetFightWolf","laser_PetFightWolf_extra","flash_pet_link","selfBurn_pet","crazy_pet","silence_pet","groupLight_pet","tenacious_pet","feedback_pet","skillGift_pet","pioneer_pet","groupReverseHurt_pet","poisonousFog_pet","pointBoom_pet","bullying_pet","flash_pet","gngerFire_pet","paralysis_pet","imploding_pet","strong_pet","trueshot_pet","recoveryHalo_pet","disabledHalo_pet","slowMoveHalo_pet","watchmanHurtNormal","watchmanHurtSuper","watchmanHurtHuman","watchmanHurtBoss","superWatchmanHurtBoss","godEyes","godMace","heroSprint","godEyesDefence","godMaceDefence","dodgeProZang","defenceZang","hurtStrikerZang","noHurtZang","sameArmsHurtAddZang","hurtZang","hurtHoleZang","SnowThinRuin","fightReduct2","SnowFattySprint","SnowFattySprintHit","hitBloodWhite","hitBloodGreen","silence_SnowSoldiers","clonedSnowSoldiers","teleport_SnowSoldiers","underCrossbow_SnowSoldiers"
         ,"SnowGirlHook","SnowGirlPull","SnowGirlHookHit","SnowGirlPullHit","SnowGirlAfterPullHit","IceManCorrosion","IceManRotate","IceManStrike","IceManShake","IceManKick","IceManSnowman","IceManRotateLink","IceManStrikeHit","IceManKickHit","snowWind","RifleHornetShooterHurt","escapeInvincible","dodge_loveSkill","girlDefence_loveSkill","girlHurt_loveSkill","lovePower_loveSkill","rebirth_loveSkill","addLifeMul_loveSkill","lifeBottle_loveSkill","element_loveSkill","elementLifeBack_loveSkill","thorns_pig","fleshFeast_pig","thunder_pig","collision_pig","roll_pig","godHand_equip","immune_equip","magneticField_equip","strongHalo_equip","murderous_equip","poisonRange_equip","attackSpeedHalo_equip","sacrifice_equip","backStrong_equip","anionSkin_equip","treater_equip","backWeak_equip","thornSkin_equip","refraction_equip","summonWolf_bigBoss","zoomOut","magneticField_enemy_link","refraction_equip_link","tauntLing","strongLing","revengeLing","resonanceLing","nightLing","clawLing","tauntLing_link","tauntLingBack_link"
         ,"revengeLing_link","Hit_SlowMove_ArmsSkill","Kill_AddCharger_ArmsSkill","Kill_AddLifeMul_ArmsSkill","Hit_Poison_ArmsSkill","Kill_Spurting_ArmsSkill","Kill_Crazy_ArmsSkill","Hit_Spurting_ArmsSkill","Hit_disabled_ArmsSkill","Hit_Paralysis_ArmsSkill","Hit_blindness_ArmsSkill","Hit_AddLifeMul_ArmsSkill","Hit_silence_ArmsSkill","Hit_hitMissile_godArmsSkill","Hit_seckill_godArmsSkill","Hit_finalkill_godArmsSkill","Hit_crazy_godArmsSkill","Hit_atry_godArmsSkill","Hit_posion7_godArmsSkill","Hit_imploding_godArmsSkill","Hit_pointBoom_godArmsSkill","Hit_fleshSkill_godArmsSkill","Hit_Lightning_godArmsSkill","Hit_Hammer_godArmsSkill","Hit_SuperMissile_godArmsSkill","Hit_Paralysis_ArmsSkill2","Hit_burn_ArmsSkill","cold_ArmsSkill","erosion_ArmsSkill","beatBack_ArmsSkill","combo_ArmsSkill","viscous_ArmsSkill","viscousSuper_ArmsSkill","flyDragonHead","bangerGunSkill","infiniteFire","shotgunBlade_ArmsSkill","godMace_ArmsSkill","windThunder_ArmsSkill","laserKill_godArmsSkill","imploding_blackArmsSkill","fear_godArmsSkill"
         ,"fear_godArmsSkill2","sickle_godArmsSkill","sickle_godArmsSkill2","sickle_godArmsSkill","sickle_godArmsSkill2","demonAddHurt","noFoggyDef","noFoggyDef","immuneNemesis","lash_ArmsSkill","beadCrossbow_ArmsSkill","editBulletPath","Hit_burn_ArmsSkill_link","fear_noBuff","sickle_godArmsSkill2_link","vehicleFit_Gaia","vehicleFit_Civilian","vehicleFit_fly","crazy_vehicle","murderous_vehicle","blade_blueMoto","WatchEagleAirDefence","alloyShell","strongStrong","shockWave","meetingGift","blade_blueMoto_link","blueMoto_state","redMoto_state","defenceLing","noHurt10Lings","resistPerLing4","hurtStrikerLing","hurtLing40","vehicleLing40","ironBodyLing","eleStrikerLing","dropStrikerLing","FightKing_slowMove","FightKing_cloned","knifeBoom_FightKing","summonedPo_extra","FightKing_cloned_extra","silence_FightKing","coverFog_FightKing","weaponEmp","weaponEmpAll","goldSpadeSkill","goldSpadeSkill","sprintSwordHit","sprintSwordHit_extra","stoneSeaBuff","pioneerDemon","demonShield","demCloned","weaponDefence"
         ,"fitVehicleDefence","immune","offAllSkill","toLand","underToLand","ruleRange","bladeShield","meteoriteRain","lightningFloor","killPet","enemyEmp","invincibleEmp","moreBullet","resistMulHurt","corpsePoison","noUnderFlyHit","noUnderLaser","revengeArrow","revengeGhost","deadlyArrow","deadlyGhost","killAllExcludeVehicle","fightBackBullet","screwBall","shortLivedDisabled","shortLivedDisabledUnderHit","summonShortLife","summonShortLifeMax","enemyToZombie","enemyToZombieDie","enemyToSpider","verShield","verShieldBuff","midLightning","godShield","lockLife","killXinLing","cantMove","noPurgoldArms","everSilenceEnemy","everNoSkillEnemy","firstLivePer10","groupLight_hero","crazy_hero","tenacious_hero","hitMissile_hero","devour_hero","pointBoom_hero","hyperopia_hero","hiding_hero","moreMissile_hero","through_hero","poisonousFog_hero","murderous_hero","bullying_hero","charm_hero","feedback_hero","skillGift_hero","silence_hero","myopia_hero","pioneer_hero","globalSpurting_hero","selfBurn_hero","accurate_hero"
         ,"lookDown_hero","eleOverlap_hero","bloodShield_hero","gliding_hero","rolling_hero","kingWing_hero","possession_hero","silverScreen_hero","invisibility_hero","screaming_hero","groupReverseHurt_hero","globalLight_hero","coquettish_hero","wisdomAnger_hero","poisonousFog_hero_link","devour_hero_link","backHurt_hero_link","selfBurn_hero_link","wisdomAnger_hero_link","rolling_hero_link","silence_hero_link","moreMissile_hero_dizziness","moreMissile_hero_dizziness2","clearGas_WatchEagle","gas_WatchEagle","sprint_WatchEagle","wind_WatchEagle","Triceratops_stone","Triceratops_hard","Triceratops_egg","Triceratops_oasis","Triceratops_deserted","noDegradation","DesertOasis_thirst","FlyDragon_fireSurround","FlyDragonBall","FlyDragon_petrifaction","FlyDragon_dodgePro","FlyDragon_summoned","FlyDragon_likeMissle","fightReduct","FlyDragon_hammer","SaberTiger_laser","SaberTiger_missile","SaberTiger_ChemicalTank","SaberTiger_shield","SaberTiger_rebirth","SaberTiger_shield_first","SaberTiger_shield_second"
         ,"SaberTiger_shield_anger","SaberTiger_shield_defence","TriceratopsHurt","ChemicalTank_Triceratops","Mammoth_die","Mammoth_electricity","Mammoth_missile","Mammoth_missileChip","Mammoth_core","Mammoth_hurt","Mammoth_coreShow","Mammoth_core_die","Mammoth_core_attached","Mammoth_core_blueElec","Mammoth_core_redElec","Mammoth_core_hurt","Mammoth_wolf","Mammoth_wolf_die","invincible_eeg","magneticField_egg","block_egg","trueshot_eeg","underHit_Paralysis_link","VanityKer_comet","VanityKer_cometBuff","VanityKer_rayBuff","VanityKer_dreamland","VanityKer_dreamlandUnit","VanityKer_feeding","VanityKer_antimatter","Antimatter_hammer","Antimatter_die","Nian_change","Nian_change_link","Nian_spark","Nian_dartsParalysis","FireWolf_rockFire","FireWolf_noFire","FireWolf_elements","FireWolf_cloned","FireWolf_noRockFire","elementsYellow","elementsRed","elementsGreen","elementsBlue","elementsPurple","Salamander_wather","Salamander_wather","Salamander_burrow","Salamander_back","Salamander_bubbles","bubbles_hammer"
         ,"bubblesGotoCamp","bubblesDie","Bubbles_blue","Bubbles_green","BubblesJump","VirtualScorpion_press","VirtualScorpion_wind","VirtualScorpion_light","VirtualScorpion_defence","VirtualScorpion_windHurt","DryFrogPull","DryFrogPullHit","DryFrogPullPosion","DryFrogRotate","DryFrogPour","DryFrogJump","eleField_FastGuards","eleField_FastGuards_link","FastGuards_screen","FastGuards_missile","FastGuards_spring","Weaver_smoke","Weaver_web","Weaver_web_hiding","Weaver_web_life","Weaver_summoned","Weaver_thorn","Weaver_thorn_hit","DuelistEffect","DuelistShake","DuelistShakeSlow","DuelistShoot","DuelistCombo","DuelistComboHit","DuelistCloned","DuelistSummoned"];
         var _ArrColor1:* = ["地震","地震-使目标防御力降低","无尽轰炮","召唤闪电塔","能量外壳","全域电击","全域电击-清除王者之剑buff","逐渐死亡","闪电","穿梭","黑洞","异龙蛋","爆竹","恐怖盒子","无疆骑士","召唤暴君","骷髅权杖","骷髅权杖-击中","惊吓","惊吓","战争抗体","坏血","猎手原形","悦动先锋","饥饿行踪","血脉沸腾","沃龙隐","沃龙隐","鹰眼","遁形","核爆","注视","搏命","永久隐身","上限","上限","魔抗","远程抵抗","只受副手伤害","只受副手伤害","只受副手伤害，指定伤害","只受弩伤害","只受古老的火炮伤害","技能免疫","永久无敌","永久无敌且不受碰撞","隐藏","自封","自闭","禁止","无法射击","自刎","逃跑","百分比攻击衰减","百分比攻击衰减","百分比攻击衰减","取消百分比伤害","酸性腐蚀","低速","超速","倍速","弹跳次数+1","不能弹跳","根据生命值扣除显示stand状态","易怒","自爆炸弹","无头自爆僵尸-启动攻击后自爆","防化","防毒","抗毒","暴击","圆周弹","导弹召唤","分身","致残","僵尸王-狂暴","远视","永久金刚钻","无限弹药","关闭金刚钻","导弹召唤-逃出升天任务","自燃","击中眩晕","震地","旋风刀","击中麻痹","狂暴","击中麻痹","吸血","凝视","藐视","快进","加攻加防","20秒后自爆","刚体","胶性表皮","空虚","巨力滚","七步毒","蚀毒","沉默","隐身","反溅","毒气弹","毒气弹-靠近自爆","毒气弹-自爆","减速","原始无敌5秒","破魅","尖叫","尸化","技能复制","隐匿之雾","地狱之刃","地狱之刃","最后一搏","致盲","瞬移","狂暴","眩晕之锤","眩晕之锤","地狱之刃-眩晕","钢铁之躯","磁力场","磁力场-曲扭光环","磁力反击","沉默","清空目标子弹","减速","蝙蝠阵","巫尸之怒","巫尸之怒-吸血","毒爪","灵步","瞬秒1","缠绕","刚毅","践踏","铁拳","净化器","踩踏","无疆统治","无疆统治-吸附","无疆统治-闪电伤害"
         ,"群体狂暴","生命连结","反哺","反转术","七步毒","野性召唤","大地之怒","铁拳冲撞","灼热视线","狼图腾","疾风斩","遁地风暴","防弹外壳","防弹钢甲","防弹钢甲","绝命攻击","狂躁","喷射毒蛛","分身","狂暴","群体圣光","群体加速","瞬移","隐身","群体隐身","电离折射","爆石","闪电麻痹","定点轰炸","导弹召唤","万弹归宗","生命置换","沉默","全局溅射","嗜爪","毒爪","自燃","欺凌","馈赠","致残","减速","顽强","减速光环","致残光环","强击光环","腐蚀","荒芜光环","重生","复原","复原光环","反击","电离反转","瞬移","反馈--反弹被动技能","闪电麻痹-减速","生命置换-置换","技能复制-复制","全局溅射-链接","减速","只受到铁锹的攻击","只受到铁铲的攻击","只受圣诞礼炮攻击","不受任何元素伤害","血条等级显示倒计时","矿石产生物品","奉献","巨伤盾三","防御力提升20%","载具攻防","继续抵挡3次百分比伤害","减少无敌敌人伤害","靠近回血","主角抵挡3次百分比伤害","召唤毒蛛","反哺","反扑","电球爆发","永久隐身","酸雨","召唤毒蛛","增加血量","增加弹药","无敌药水","增加移动速度","召唤摩卡","无敌隐身-重生石","无敌隐身","月饼增加攻击力","汤圆增加攻击力","饺子无敌状态","双倍黑色武器掉落","双倍黑色装备掉落","双倍黑色武器掉落","双倍黑色装备掉落","神宠卡片","神车卡片","4倍载具伤害","磁力场","超级散射卡","骷髅卡","骷髅卡扣血","后来居上","夜空隐","狼震","金翼","龙翅","恶魔风脚","飞雷神","疾风斩","瞬秒2","小炎戒","英雄跳斩-降低目标攻击力","队长之魄","鼓舞士气","盛怒","无敌之怒","生化锁定","生化锁定X","动能推撞","超能推撞","挖掘者-动能推撞-推","技能衰减","竞技场-伤害加倍","击中减速","减速光环","主动加血","反弹伤害","怪物带着南关头","花火面具","沃龙面具","丛安面具","超级散射","无限馈赠","极限射速","战争狂人脱下头盔","丛林特种兵技能"
         ,"瞬秒3","攻击无视闪避","红外眼","无敌自爆僵尸攻击主角","狙击之王任务主角状态","狙击之王怪物攻击主角","狙击之王怪物受攻击","狙击之王怪物受攻击-技能","子弹碰撞","蝙蝠状态","步枪敏感","狙击敏感","散弹敏感","手枪敏感","火炮敏感","弩敏感","喷火器敏感","激光枪敏感","其他敏感","副手敏感","载具敏感","尸宠敏感","红武敏感","徒手敏感","巫尸之怒","猎击","酸性腐蚀","旋风刀","震地","狂刃追踪","远视","极速炮轰","补给头盔","弹性世界","全域圣光","聚能力量","飓风","巫尸之怒","牺牲","防御光环","上帝的护佑","无尽轰炸","聚能电流","退化光环","野性召唤","灼热射线","狼图腾","大地之怒","充能","辐射光球","静电过载","敏捷光环","致盲","减速","眩晕","灼热射线-附带技能","闪烁-目标点爆炸","自燃","狂暴","沉默","群体圣光","反击","电离折射","馈赠","先锋盾","电离反转","毒雾","定点轰炸","欺凌","闪烁","怒火","闪电麻痹","爆石","顽强","强击光环","复原光环","致残光环","减速光环","怪物猎人","精英克星","人类克星","恶魔猎手","恶魔屠刀","上帝之眼","上帝之杖","鬼步","上帝之眼-防御力降低","上帝之杖-防御力降低","闪避提升20%","防御力提升30%","主角攻击力提升10%","抵挡伤害","相同武器伤害叠加","攻击力提升30%","伤害光环150","雪藏","近战防御","千斤顶","千斤顶","白色血液效果","绿色血液效果","击中沉默","分身","风暴突袭","收到攻击打断风暴突袭","钩拉","钩雪莲华","钩拉-击中麻痹","钩雪莲华-击中","钩雪莲华-抛空中","腐蚀","飞雪连舞","雪引冲锋","爆怒一击","踢爆","召唤雪人","飞雪连舞","雪引冲锋-冲撞","踢爆-冲撞","飓风载体","诱击对玩家单位伤害降低","摩卡护体","敏捷馈赠","防御力提升","攻击力提升","真情之力","重生","回复","真情治愈","分子虹吸","分子虹吸-自身回血","荆棘外壳","血肉盛宴","雷霆斧击","野性冲锋","巨力滚"
         ,"上帝的护佑","免疫","磁力场","顽强光环","嗜爪之怒","瘴气","耐久光环","牺牲","钢背","负离子外壳","净化器","芒刺","荆棘外表","折射","召唤群狼","装甲压制","磁力场-曲扭光环","折射","嘲讽","遇强则刚","复仇","共鸣","暗夜信徒","恶爪","嘲讽敌人","反弹伤害","复仇伤害","击中减速","击毙补充弹药","击毙回复","剧毒","击毙溅射","振奋","击中溅射","致残","击中麻痹","致盲","击中回复","击中沉默","击中派生","瞬秒4","绝灭","快感","搏命","七步毒","爆石","引爆","刷新","击中闪电","击中眩晕","超级派生","超级麻痹","炎爆","冷凝","蚀骨","痛击","连弩","粘性","超级粘性","火焰始祖","贯穿波","无限火力","跳斩","上帝之杖","风雷","影灭","爆沙","爆胆","爆震","破甲","溃甲","飞镰","跟踪飞镰","战修罗","突袭","突袭","惩罚","子母弹","烟花","轨迹编辑","炎爆-爆","爆胆-无法叠加得buff","超级飞镰-破甲","轰天聚合","镇山聚合","霸空聚合","核弹头","核动力","冻血刀锋","守望之盾","合金外壳","遇强则强","冲击波","见面礼","降低攻击力","特效","特效","防御力提升","抵挡低于自身生命值的伤害。","抵挡4次百分比伤害","主角攻击力提升","攻击力提升40%","载具防御力","钢铁之躯","指定元素加成","掉率提升","狂战尸-震地减速","分身","狂刃爆发","召唤冥刃","分身","沉默","金钟雾","电离驱散","电离驱散-任何","加持","崩溃","凯撒特效附带","凯撒特效附带","石海buff","先锋盾","能量外壳","分身","副手防御","聚合防御","免疫","封锁","击落","受击坠落","统治圈","利刃盾","陨石雨","大地闪电","破宠","电离驱散","无敌驱散","超级散射","巨伤盾","尸毒","防空盾","激光盾","复仇之箭","复仇之魂","夺命箭","夺命魂","瞬秒所有除了载具","反击导弹","旋转电球","短命之仇","短命之仇-受到攻击","折寿","薄命","变尸"
         ,"死后杀死宿主","变蛛","竖盾","竖盾buff","强电","上帝之盾","锁血","瞬秒心零","监禁","禁无双","永久沉默","永久封锁","衰竭","群体圣光","狂暴","反击","派生导弹","吞噬","定点轰炸","远视","群体隐身","万弹归宗","金刚钻","毒雾","嗜爪","欺凌","魅惑","电离折射","馈赠","沉默","近视","先锋盾","全局溅射","群体自燃","精准","藐视","元素叠加","血盾","滑翔","翻滚","王者之翼","附身","全域光波","隐匿之雾","尖叫","反转术","全域圣光","妖魅","智慧怒火","毒雾-减血","吞噬-回血","反馈--反弹被动技能","自燃-链接","自燃-链接","翻滚-减速","沉默-使我方技能免疫3秒","击中眩晕","击中眩晕","清除燃气","燃气","原力俯冲","焦灼烈焰","巨石崩塌","硬甲","蛋护","海市蜃楼","饥荒","远古基因","解渴","巨焰围剿","月石","石化","矫捷姿态","召唤蝙蝠","龙甲","近战防御","石化眩晕","量子光束","末日轰炸","召唤生化罐","超导电幕","机械重组","超导电幕-第一阶段","超导电幕-第二阶段","超导电幕-怒气","超导电幕-防御","异角龙百分比伤害","召唤异角龙","死后清除所有召唤单位","电磁风暴","洲际导弹","洲际导弹-自爆","机械核心","钛钢甲","召唤机械核心","机械核心-死亡","机械核心-附着","机械核心-蓝闪电","机械核心-红闪电","机械核心-受伤","狼狈为奸","尸狼-死亡","无敌光环","磁力场","墙","强击光环","被击中麻痹","星际尘埃","星辰buff","解析射线","虚幻镜像","召唤随从","反哺","反物质","击中眩晕","击中死亡","恶魔火","自燃-链接","驾驭星火","击中麻痹","墟洞岩火","无名火","稀有元素","孪生之力","无法释放墟洞岩火buff","打断墟洞岩火","对玩家造成伤害","对分身造成伤害","使自身恐惧","使首领恐惧","水卷风","水汽溅腾","地精","融弹回血","反转气泡","眩晕","转变颜色","死亡","蓝","绿","弹跳","泰山压顶","蝎舞风暴"
         ,"战栗光","潮汐护甲","阳风增加攻击力","毒舌钩","炸裂者-舌头钩-击中","舌头钩-击中后移动则受到伤害","布雷风暴","地雷陷阱","暗影突袭","静电场","静电场-吸附","致命打击","超级弹幕","极速伤害","瘟疫","织网","织网-隐身","织网-回血","繁殖","复仇之刺","复仇之刺-击中被拖行","决斗者-特效预处理","旋风斩","旋风斩-减速","旋风波","决斗术","决斗术-击晕目标","分身术","召唤古惑"];
         var j:int = 0;
         ArrColor0 = ["white","green","blue","purple","orange","red","black","darkgold","purgold","yagold"];
         ArrColor1 = ["白","绿","蓝","紫","橙","红","黑","暗金","紫金","氩星"];
         var Arr_type0:* = ["rifle","sniper","shotgun","pistol","rocket","crossbow","flamer","howitzer","wavegun","laser","lightning","weather"];
         var Arr_type1:* = ["步枪","狙击枪","散弹枪","手枪","火炮","弩","喷火器","榴弹炮","波动枪","激光枪","闪电枪","气象枪"];
         var ArmsNow:* = null;
         ArrNum = str0.split("&",str0.length);
         s0.inData_byObj(this.nowData.save);
         for each(ArmsNow in ArrNum)
         {
            ArrNow = ArmsNow.split("*",ArmsNow.length);
            if(ArrNow[0] == "00" || ArrNow[0] == "武器名字")
            {
               s0.cnName = ArrNow[1];
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器名称为","#fd397b") + String(String(String(String(String(String(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "01" || ArrNow[0] == "武器品质")
            {
               i = 0;
               while(i < ArrColor0.length)
               {
                  if(ArrNow[1] == ArrColor1[i])
                  {
                     s0.color = ArrColor0[i];
                     Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器颜色为","#fd397b") + ArrColor0[i] + "成功！");
                  }
                  i++;
                  Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器颜色为","#fd397b") + ArrColor0[i] + "成功！");
               }
            }
            else if(ArrNow[0] == "02" || ArrNow[0] == "武器类型")
            {
               j = 0;
               while(j < Arr_type0.length)
               {
                  if(ArrNow[1] == Arr_type1[j])
                  {
                     s0.armsType = Arr_type0[j];
                     Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器类型","#fd397b") + Arr_type0[j] + "成功！");
                  }
                  j++;
                  Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器类型","#fd397b") + Arr_type0[j] + "成功！");
               }
            }
            else if(ArrNow[0] == "03" || ArrNow[0] == "武器弹容")
            {
               s0.capacity = int(int(int(int(int(int(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器弹容为","#fd397b") + int(int(int(int(int(int(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "04" || ArrNow[0] == "武器单发")
            {
               s0.bulletNum = int(int(int(int(int(int(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器单发为","#fd397b") + int(int(int(int(int(int(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "05" || ArrNow[0] == "武器等级")
            {
               s0.itemsLevel = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器等级为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "06" || ArrNow[0] == "武器射速")
            {
               s0.attackGap = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器射速为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "07" || ArrNow[0] == "下一页")
            {
               Gaming.uiGroup.alertBox.textInput.showTextInput("换弹速度[08*数值]武器伤害[09*数值]\n强化等级[10*数值]进化等级[11*数值]\n武器射程[12*数值]武器抖动[13*数值]\n瞬发概率[14*数值]|下一页[15*可空]|","",this.ArmsEdit1a);
            }
            else if(ArrNow[0] == "08" || ArrNow[0] == "换弹速度")
            {
               s0.reloadGap = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置换弹速度为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "09" || ArrNow[0] == "武器伤害")
            {
               s0.hurtRatio = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器伤害为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "10" || ArrNow[0] == "强化等级")
            {
               s0.strengthenLv = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置强化等级为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "11" || ArrNow[0] == "进化等级")
            {
               s0.evoLv = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置进化等级为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "12" || ArrNow[0] == "武器射程")
            {
               s0.bulletWidth = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器射程为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "13" || ArrNow[0] == "武器抖动")
            {
               s0.shakeAngle = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器抖动为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "14" || ArrNow[0] == "瞬发概率")
            {
               s0.twoShootPro = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置瞬发概率为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "16" || ArrNow[0] == "穿人个数")
            {
               s0.penetrationNum = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置穿人个数为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "17" || ArrNow[0] == "穿墙深度")
            {
               s0.penetrationGap = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置穿墙深度为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "18" || ArrNow[0] == "暴击倍数")
            {
               s0.critD.mul = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置暴击倍数为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "19" || ArrNow[0] == "暴击概率")
            {
               s0.critD.pro = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置暴击概率为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "20" || ArrNow[0] == "击中反弹")
            {
               s0.bounceD.body = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置击中反弹为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "21" || ArrNow[0] == "地面反弹")
            {
               s0.bounceD.floor = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置地面反弹为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "22" || ArrNow[0] == "设置模型")
            {
               s0.armsImgLabel = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置模型代码为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            if(ArrNow[0] == "24" || ArrNow[0] == "复制模型")
            {
               System.setClipboard(s0.armsImgLabel);
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("复制模型代码","#fd397b") + "成功！");
            }
            else if(ArrNow[0] == "25" || ArrNow[0] == "武器枪声")
            {
               s0.shootSoundUrl = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器枪声为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "26" || ArrNow[0] == "武器技能")
            {
               if(ArrNow[1] == "*全部添加所有技能" || ArrNow[1] == "全部添加所有技能")
               {
                  s0.skillArr = _ArrColor0.concat([]);
                  Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("添加所有技能","#fd397b") + "成功！共添加" + _ArrColor0.length + "个技能！");
               }
               else
               {
                  i = 0;
                  while(i < _ArrColor0.length)
                  {
                     if(ArrNow[1] == _ArrColor1[i])
                     {
                        s0.skillArr = [_ArrColor0[i]];
                        Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器技能","#fd397b") + "成功！");
                        break;
                     }
                     i++;
                  }
                  if(i >= _ArrColor0.length)
                  {
                     Gaming.uiGroup.alertBox.showError("未找到技能：" + ArrNow[1]);
                  }
               }
            }
         }
         this.nowData.save = s0;
         // 标记武器为已修改状态
         this.nowData.save.lockB = true;
         // 添加兼容性标记，防止其他版本重置数据
         this.nowData.save.customEditB = true;
         // 刷新武器数据以确保修改生效
         if(this.nowData.placeType == "bag")
         {
            this.nowData.fleshData_byEquip(Gaming.PG.DATA.equip.mergeData);
         }
         else
         {
            this.nowData.fleshData_byMeEquip();
         }
         Gaming.uiGroup.allBagUI.fleshAllBox();
         this.showOneArmsDataAndPan(this.nowData);
      }
      
      private function ArmsEdit1a(str0:String) : void
      {
         var s0:ArmsSave = new ArmsSave();
         var ArrNum:Array = new Array();
         var ArrNow:Array = new Array();
         var ArmsNow:* = null;
         ArrNum = str0.split("&",str0.length);
         s0.inData_byObj(this.nowData.save);
         for each(ArmsNow in ArrNum)
         {
            ArrNow = ArmsNow.split("*",ArmsNow.length);
            if(ArrNow[0] == "08")
            {
               s0.reloadGap = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置换弹速度为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "09")
            {
               s0.hurtRatio = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器伤害为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "10")
            {
               s0.strengthenLv = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置强化等级为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "11")
            {
               s0.evoLv = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置进化等级为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "12")
            {
               s0.bulletWidth = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器射程为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "13")
            {
               s0.shakeAngle = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器抖动为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "14")
            {
               s0.twoShootPro = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置瞬发概率为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "16" || ArrNow[0] == "穿人个数")
            {
               s0.penetrationNum = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置穿人个数为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "17" || ArrNow[0] == "穿墙深度")
            {
               s0.penetrationGap = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置穿墙深度为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "18" || ArrNow[0] == "暴击倍数")
            {
               s0.critD.mul = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置暴击倍数为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "19" || ArrNow[0] == "暴击概率")
            {
               s0.critD.pro = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置暴击概率为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "20" || ArrNow[0] == "击中反弹")
            {
               s0.bounceD.body = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置击中反弹为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "21" || ArrNow[0] == "地面反弹")
            {
               s0.bounceD.floor = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置地面反弹为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "22" || ArrNow[0] == "模型代码")
            {
               s0.armsImgLabel = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置模型代码为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "15")
            {
               Gaming.uiGroup.alertBox.textInput.showTextInput("穿人个数[16*数值]穿墙深度[17*数值]\n暴击倍数[18*数值]暴击概率[19*数值]\n击中反弹[20*数值]地面反弹[21*数值]\n模型代码[22*数值]|下一页[23*可空]|","",this.ArmsEditb);
            }
         }
         this.nowData.save = s0;
         // 标记武器为已修改状态
         this.nowData.save.lockB = true;
         // 添加兼容性标记，防止其他版本重置数据
         this.nowData.save.customEditB = true;
         // 刷新武器数据以确保修改生效
         if(this.nowData.placeType == "bag")
         {
            this.nowData.fleshData_byEquip(Gaming.PG.DATA.equip.mergeData);
         }
         else
         {
            this.nowData.fleshData_byMeEquip();
         }
         Gaming.uiGroup.allBagUI.fleshAllBox();
         this.showOneArmsDataAndPan(this.nowData);
      }
      
      private function ArmsEditb(str0:String) : void
      {
         var s0:ArmsSave = new ArmsSave();
         var ArrNum:Array = new Array();
         var ArrNow:Array = new Array();
         var ArmsNow:* = null;
         ArrNum = str0.split("&",str0.length);
         s0.inData_byObj(this.nowData.save);
         for each(ArmsNow in ArrNum)
         {
            ArrNow = ArmsNow.split("*",ArmsNow.length);
            if(ArrNow[0] == "16")
            {
               s0.penetrationNum = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置穿人个数为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "17")
            {
               s0.penetrationGap = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置穿墙深度为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "18")
            {
               s0.critD.mul = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置暴击倍数为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "19")
            {
               s0.critD.pro = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置暴击概率为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "20")
            {
               s0.bounceD.body = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置击中反弹为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "21")
            {
               s0.bounceD.floor = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置地面反弹为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "22")
            {
               s0.armsImgLabel = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置模型代码为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "23")
            {
               Gaming.uiGroup.alertBox.textInput.showTextInput("模型代码[24*可空]武器枪声[25*数值]\n武器技能[26*名字或*全部添加所有技能]无[27*数值]\n无[28*数值]无[29*数值]\n[30*数值]|无[31*可空]|","",this._ArmsEdit);
            }
         }
         this.nowData.save = s0;
         // 标记武器为已修改状态
         this.nowData.save.lockB = true;
         // 添加兼容性标记，防止其他版本重置数据
         this.nowData.save.customEditB = true;
         // 刷新武器数据以确保修改生效
         if(this.nowData.placeType == "bag")
         {
            this.nowData.fleshData_byEquip(Gaming.PG.DATA.equip.mergeData);
         }
         else
         {
            this.nowData.fleshData_byMeEquip();
         }
         Gaming.uiGroup.allBagUI.fleshAllBox();
         this.showOneArmsDataAndPan(this.nowData);
      }
      
      private function _ArmsEdit(str0:String) : void
      {
         var i:int = 0;
         var s0:ArmsSave = new ArmsSave();
         var ArrNum:Array = new Array();
         var ArrNow:Array = new Array();
         var ArrColor0:* = ["earthquake","earthquake_link","endlessRocket","lightningTower","energyShield","electricDevicer","electricDevicerClearKing","lightningTower_die","lightningTower_lightning","shuttleDevicer","blackHoleDevicer","dinosaurEgg","squibDevice","terroristBox","knightsMedal","skeletonMedal","skeletonWand","skeletonWandHit","terroristBox_screaming","squibDevice_screaming","outfit_follow","outfit_blood","outfit_shootMissile","outfit_jump","outfit_crit","outfit_boom","outfit_wolong","outfit_wolong_hurt","outfit_eagle","outfit_elephant","nuclear_peak","gaze_peak","sacrifice_peak","invisibilityEver","upperLimitSecond","upperLimitSecond2","noSkillHurt","noBulletHurt","onlyWeaponHurt","onlyWeaponHurtBattle","onlyWeaponHurtSet","onlyUnderCrossbow","onlyUnderOldRocket","State_SpellImmunity","State_Invincible","State_InvincibleThrough","State_noAiFind","State_noAllSkill","noAttackOrder","noMoveSpeed","noArmsShoot","killMeTimeOver","aiExcape","sumBossAtten","sumBossAtten2","sumBossAtten3"
         ,"offBossAtten","metalCorrosion","State_lowMove","State_AddMove","State_AddMove50","jumpNumAdd1","jumpNumAddZero","standImageByLife","UnderRos_AddMove_Battle","boom_headless","suicide_headless","cmldef_enemy","cmldef2_enemy","cmldef3_enemy","Hit_Crit_Fat","circle_inward_shell","sweep_shell","Doctor2_cloned","FightKing_disabled","crazy_king","hyperopia_incapable","through_enemy","noBulletReduct","throughClose","sweep_runAway","selfBurn_task","hammer_hit","choppedAttack_FightShooter","windAttack_FightShooter","FightKing_hitParalysis","FightKing_crazy","SmallSpider_hitParalysis","suckBlood_enemy","gaze_enemy","despise_enemy","fastForward_enemy","hurtDefence_enemy","suicide_treasure","rigidBody_enemy","defenceBounce_enemy","noBounce_enemy","roll_hugePosion","posion7_hugePosion","corrosion_hugePosion","silence_hugePosion","hiding_hugePosion","splash_HugePoison","summonedGasBom_HugePoison","suicide_GasBomb","selfBoom_GasBomb","slowMove_GasBomb","noUnderHurt5_GasBomb","killCharm","screaming_enemy"
         ,"changeToZombie_enemy","skillCopy_enemy","invisibility_enemy","knife_skeleton","knife_skeleton2","atry_skeleton","blindness_skeleton","teleport_skeleton","crazy_skeleton","hammer_enemy","hammer_enemy_link","knife_skeleton_hammer","ironBody_enemy","magneticField_enemy","magneticField_enemy_link","beatBack_iron","silence_wind","emp_wind","slowMove_wind","bar_wind","wizardAnger_wind","wizardAnger_wind_blood","poisonClaw_wind","noSR","xxx_wind","winding_mummy","noSpeedReduce","crazy_knights","hammer_knights","treater_knights","trample_knights","boundless_enemy","boundless_enemy_link","boundless_enemy_pass","groupCrazy_enemy","lifeLink_wolf","feeding_wolf","groupReverseHurt_enemy","posion7_wolf","summon_FightWolf","anger_FightWolf","hammer_FightWolf","laser_FightWolf","treater_FightWolf","NuggetsShoot","wind_Nuggets","likeMissle_Shapers","likeMissleNo","likeMissleNo2","missile_Sentry","addMove_Crawler","summonedSpider_Crawler","Crawler_cloned","crazy_enemy","groupLight_enemy","groupSpeedUp_enemy"
         ,"teleport_enemy","hiding_enemy","hidingAll_enemy","feedback_enemy","imploding_enemy","paralysis_enemy","pointBoom_enemy","sweep_enemy","moreMissile_enemy","liveReplace_enemy","silence_enemy","globalSpurting_enemy","murderous_enemy","poisonClaw_enemy","selfBurn_enemy","bullying_enemy","skillGift_enemy","disabled_enemy","slowMove_enemy","strong_enemy","slowMoveHalo_enemy","disabledHalo_enemy","trueshot_enemy","corrosion_enemy","desertedHalo_enemy","rebirth_enemy","recovery_enemy","recoveryHalo_enemy","tenacious_enemy","reverseHurt_enemy","teleport_enemy15","backHurt_enemy_link","paralysis_enemy_link","lifeReplace_enemy_link","skillCopy_enemy_link","globalSpurting_enemy_link","slowMoveNoClear","onlyUnderMiningSpade","onlyUnderMiningShovels","onlyUnderChristmasGun","noEleUnder","lifeBarShowCd","orcCreateThings","dedicationLove","resistPerLove2","defenceAddLove","vehicleAddLove30","resistPerLoveAdd2","underInvincibleHurtLove","nearAddLifeLove","mainResistPerLove","summonedSpider_spiderKing"
         ,"feeding_spider","pounce_spider","electricBoom_enemy","invisibility_SpiderKing","acidRain_SpiderKing","summonedSpider_spiderKing_extra","DropEffect_AddLifeMul","DropEffect_AddChargerMul","invincibleDrugDrop","DropEffect_AddMoveSpeedMul","addMocha","godHiding_things","godHiding_Pet","moonCake","tangyuan","jiaozi","armsDropDouble","equipDropDouble","armsDropDoubleAndGem","equipDropDoubleAndEquipGem","highPetCard","highVehicleCard","highCardState","electromagnet","superSpreadCard","skeletonCard","skeletonCard_link","pumpkinHead","pumpkinHead","wolfFashionSkill","goldFalcon","dragonHeadSkill","crazy_sanji","xiaoBoShoot","xiaoMingShoot","seckillNormalEnemy","xiaoAiShoot","shotgunBladeHero","chinaCaptainSkill","armyCommanderSkill","bladeSkill","cyanArmySkill","bioShockSkill","hurtBossAdd","highDrill","superHighDrill","highDrillHit","extendCd","arenaHurtAdd","Hit_SlowMove","Range_SlowMove","Manual_AddLife","BackHurt","pumpkinDropEffect","gmMask1","gmMask2","gmMask3","superMoreBullet","superSkillGift"
         ,"maxSpeedTask","madmanHead","betHit","killAll","attackNoDodge","findHide","invincibleZombieEnemyHit","sniperKingBuff","sniperKingEnemyHit","sniperKingEnemyUnder","sniperKingEnemyUnder2","bulletRainBallHit","flySkyBatBuff","rifleSensitive","sniperSensitive","shotgunSensitive","pistolSensitive","rocketSensitive","crossbowSensitive","flamerSensitive","laserSensitive","otherSensitive","weaponSensitive","vehicleSensitive","petSensitive","redArmsSensitive","handSensitive","wizardAngerEdit","huntParts","acidicParts","whirlwind_FightKing","shake_FightKing","shoot_FightKing","hyperopia_pet","shelling_ZombieKing","helmet_PetZombieFootball","shoot_PetZombieFootball","globalLight_PetZombieFootball","shoot2_PetTyphoonWitch","wind_PetTyphoonWitch","wizardAnger_PetTyphoonWitch","sacrifice_PetIronChief","defenceAuras_PetIronChief","godHand_PetIronChief","endlessBombing_skull","current_skull","degradation_PetBoomSkull","summon_PetFightWolf","laser_PetFightWolf","treater_PetFightWolf","anger_PetFightWolf"
         ,"charged_PetLake","lightBall_PetLake","static_PetLake","agile_PetLake","blindness_anger_PetFightWolf","lightBall_PetLake_slow","dizziness_anger_PetFightWolf","laser_PetFightWolf_extra","flash_pet_link","selfBurn_pet","crazy_pet","silence_pet","groupLight_pet","tenacious_pet","feedback_pet","skillGift_pet","pioneer_pet","groupReverseHurt_pet","poisonousFog_pet","pointBoom_pet","bullying_pet","flash_pet","gngerFire_pet","paralysis_pet","imploding_pet","strong_pet","trueshot_pet","recoveryHalo_pet","disabledHalo_pet","slowMoveHalo_pet","watchmanHurtNormal","watchmanHurtSuper","watchmanHurtHuman","watchmanHurtBoss","superWatchmanHurtBoss","godEyes","godMace","heroSprint","godEyesDefence","godMaceDefence","dodgeProZang","defenceZang","hurtStrikerZang","noHurtZang","sameArmsHurtAddZang","hurtZang","hurtHoleZang","SnowThinRuin","fightReduct2","SnowFattySprint","SnowFattySprintHit","hitBloodWhite","hitBloodGreen","silence_SnowSoldiers","clonedSnowSoldiers","teleport_SnowSoldiers","underCrossbow_SnowSoldiers"
         ,"SnowGirlHook","SnowGirlPull","SnowGirlHookHit","SnowGirlPullHit","SnowGirlAfterPullHit","IceManCorrosion","IceManRotate","IceManStrike","IceManShake","IceManKick","IceManSnowman","IceManRotateLink","IceManStrikeHit","IceManKickHit","snowWind","RifleHornetShooterHurt","escapeInvincible","dodge_loveSkill","girlDefence_loveSkill","girlHurt_loveSkill","lovePower_loveSkill","rebirth_loveSkill","addLifeMul_loveSkill","lifeBottle_loveSkill","element_loveSkill","elementLifeBack_loveSkill","thorns_pig","fleshFeast_pig","thunder_pig","collision_pig","roll_pig","godHand_equip","immune_equip","magneticField_equip","strongHalo_equip","murderous_equip","poisonRange_equip","attackSpeedHalo_equip","sacrifice_equip","backStrong_equip","anionSkin_equip","treater_equip","backWeak_equip","thornSkin_equip","refraction_equip","summonWolf_bigBoss","zoomOut","magneticField_enemy_link","refraction_equip_link","tauntLing","strongLing","revengeLing","resonanceLing","nightLing","clawLing","tauntLing_link","tauntLingBack_link"
         ,"revengeLing_link","Hit_SlowMove_ArmsSkill","Kill_AddCharger_ArmsSkill","Kill_AddLifeMul_ArmsSkill","Hit_Poison_ArmsSkill","Kill_Spurting_ArmsSkill","Kill_Crazy_ArmsSkill","Hit_Spurting_ArmsSkill","Hit_disabled_ArmsSkill","Hit_Paralysis_ArmsSkill","Hit_blindness_ArmsSkill","Hit_AddLifeMul_ArmsSkill","Hit_silence_ArmsSkill","Hit_hitMissile_godArmsSkill","Hit_seckill_godArmsSkill","Hit_finalkill_godArmsSkill","Hit_crazy_godArmsSkill","Hit_atry_godArmsSkill","Hit_posion7_godArmsSkill","Hit_imploding_godArmsSkill","Hit_pointBoom_godArmsSkill","Hit_fleshSkill_godArmsSkill","Hit_Lightning_godArmsSkill","Hit_Hammer_godArmsSkill","Hit_SuperMissile_godArmsSkill","Hit_Paralysis_ArmsSkill2","Hit_burn_ArmsSkill","cold_ArmsSkill","erosion_ArmsSkill","beatBack_ArmsSkill","combo_ArmsSkill","viscous_ArmsSkill","viscousSuper_ArmsSkill","flyDragonHead","bangerGunSkill","infiniteFire","shotgunBlade_ArmsSkill","godMace_ArmsSkill","windThunder_ArmsSkill","laserKill_godArmsSkill","imploding_blackArmsSkill","fear_godArmsSkill"
         ,"fear_godArmsSkill2","sickle_godArmsSkill","sickle_godArmsSkill2","sickle_godArmsSkill","sickle_godArmsSkill2","demonAddHurt","noFoggyDef","noFoggyDef","immuneNemesis","lash_ArmsSkill","beadCrossbow_ArmsSkill","editBulletPath","Hit_burn_ArmsSkill_link","fear_noBuff","sickle_godArmsSkill2_link","vehicleFit_Gaia","vehicleFit_Civilian","vehicleFit_fly","crazy_vehicle","murderous_vehicle","blade_blueMoto","WatchEagleAirDefence","alloyShell","strongStrong","shockWave","meetingGift","blade_blueMoto_link","blueMoto_state","redMoto_state","defenceLing","noHurt10Lings","resistPerLing4","hurtStrikerLing","hurtLing40","vehicleLing40","ironBodyLing","eleStrikerLing","dropStrikerLing","FightKing_slowMove","FightKing_cloned","knifeBoom_FightKing","summonedPo_extra","FightKing_cloned_extra","silence_FightKing","coverFog_FightKing","weaponEmp","weaponEmpAll","goldSpadeSkill","goldSpadeSkill","sprintSwordHit","sprintSwordHit_extra","stoneSeaBuff","pioneerDemon","demonShield","demCloned","weaponDefence"
         ,"fitVehicleDefence","immune","offAllSkill","toLand","underToLand","ruleRange","bladeShield","meteoriteRain","lightningFloor","killPet","enemyEmp","invincibleEmp","moreBullet","resistMulHurt","corpsePoison","noUnderFlyHit","noUnderLaser","revengeArrow","revengeGhost","deadlyArrow","deadlyGhost","killAllExcludeVehicle","fightBackBullet","screwBall","shortLivedDisabled","shortLivedDisabledUnderHit","summonShortLife","summonShortLifeMax","enemyToZombie","enemyToZombieDie","enemyToSpider","verShield","verShieldBuff","midLightning","godShield","lockLife","killXinLing","cantMove","noPurgoldArms","everSilenceEnemy","everNoSkillEnemy","firstLivePer10","groupLight_hero","crazy_hero","tenacious_hero","hitMissile_hero","devour_hero","pointBoom_hero","hyperopia_hero","hiding_hero","moreMissile_hero","through_hero","poisonousFog_hero","murderous_hero","bullying_hero","charm_hero","feedback_hero","skillGift_hero","silence_hero","myopia_hero","pioneer_hero","globalSpurting_hero","selfBurn_hero","accurate_hero"
         ,"lookDown_hero","eleOverlap_hero","bloodShield_hero","gliding_hero","rolling_hero","kingWing_hero","possession_hero","silverScreen_hero","invisibility_hero","screaming_hero","groupReverseHurt_hero","globalLight_hero","coquettish_hero","wisdomAnger_hero","poisonousFog_hero_link","devour_hero_link","backHurt_hero_link","selfBurn_hero_link","wisdomAnger_hero_link","rolling_hero_link","silence_hero_link","moreMissile_hero_dizziness","moreMissile_hero_dizziness2","clearGas_WatchEagle","gas_WatchEagle","sprint_WatchEagle","wind_WatchEagle","Triceratops_stone","Triceratops_hard","Triceratops_egg","Triceratops_oasis","Triceratops_deserted","noDegradation","DesertOasis_thirst","FlyDragon_fireSurround","FlyDragonBall","FlyDragon_petrifaction","FlyDragon_dodgePro","FlyDragon_summoned","FlyDragon_likeMissle","fightReduct","FlyDragon_hammer","SaberTiger_laser","SaberTiger_missile","SaberTiger_ChemicalTank","SaberTiger_shield","SaberTiger_rebirth","SaberTiger_shield_first","SaberTiger_shield_second"
         ,"SaberTiger_shield_anger","SaberTiger_shield_defence","TriceratopsHurt","ChemicalTank_Triceratops","Mammoth_die","Mammoth_electricity","Mammoth_missile","Mammoth_missileChip","Mammoth_core","Mammoth_hurt","Mammoth_coreShow","Mammoth_core_die","Mammoth_core_attached","Mammoth_core_blueElec","Mammoth_core_redElec","Mammoth_core_hurt","Mammoth_wolf","Mammoth_wolf_die","invincible_eeg","magneticField_egg","block_egg","trueshot_eeg","underHit_Paralysis_link","VanityKer_comet","VanityKer_cometBuff","VanityKer_rayBuff","VanityKer_dreamland","VanityKer_dreamlandUnit","VanityKer_feeding","VanityKer_antimatter","Antimatter_hammer","Antimatter_die","Nian_change","Nian_change_link","Nian_spark","Nian_dartsParalysis","FireWolf_rockFire","FireWolf_noFire","FireWolf_elements","FireWolf_cloned","FireWolf_noRockFire","elementsYellow","elementsRed","elementsGreen","elementsBlue","elementsPurple","Salamander_wather","Salamander_wather","Salamander_burrow","Salamander_back","Salamander_bubbles","bubbles_hammer"
         ,"bubblesGotoCamp","bubblesDie","Bubbles_blue","Bubbles_green","BubblesJump","VirtualScorpion_press","VirtualScorpion_wind","VirtualScorpion_light","VirtualScorpion_defence","VirtualScorpion_windHurt","DryFrogPull","DryFrogPullHit","DryFrogPullPosion","DryFrogRotate","DryFrogPour","DryFrogJump","eleField_FastGuards","eleField_FastGuards_link","FastGuards_screen","FastGuards_missile","FastGuards_spring","Weaver_smoke","Weaver_web","Weaver_web_hiding","Weaver_web_life","Weaver_summoned","Weaver_thorn","Weaver_thorn_hit","DuelistEffect","DuelistShake","DuelistShakeSlow","DuelistShoot","DuelistCombo","DuelistComboHit","DuelistCloned","DuelistSummoned"];
         var ArrColor1:* = ["地震","地震-使目标防御力降低","无尽轰炮","召唤闪电塔","能量外壳","全域电击","全域电击-清除王者之剑buff","逐渐死亡","闪电","穿梭","黑洞","异龙蛋","爆竹","恐怖盒子","无疆骑士","召唤暴君","骷髅权杖","骷髅权杖-击中","惊吓","惊吓","战争抗体","坏血","猎手原形","悦动先锋","饥饿行踪","血脉沸腾","沃龙隐","沃龙隐","鹰眼","遁形","核爆","注视","搏命","永久隐身","上限","上限","魔抗","远程抵抗","只受副手伤害","只受副手伤害","只受副手伤害，指定伤害","只受弩伤害","只受古老的火炮伤害","技能免疫","永久无敌","永久无敌且不受碰撞","隐藏","自封","自闭","禁止","无法射击","自刎","逃跑","百分比攻击衰减","百分比攻击衰减","百分比攻击衰减","取消百分比伤害","酸性腐蚀","低速","超速","倍速","弹跳次数+1","不能弹跳","根据生命值扣除显示stand状态","易怒","自爆炸弹","无头自爆僵尸-启动攻击后自爆","防化","防毒","抗毒","暴击","圆周弹","导弹召唤","分身","致残","僵尸王-狂暴","远视","永久金刚钻","无限弹药","关闭金刚钻","导弹召唤-逃出升天任务","自燃","击中眩晕","震地","旋风刀","击中麻痹","狂暴","击中麻痹","吸血","凝视","藐视","快进","加攻加防","20秒后自爆","刚体","胶性表皮","空虚","巨力滚","七步毒","蚀毒","沉默","隐身","反溅","毒气弹","毒气弹-靠近自爆","毒气弹-自爆","减速","原始无敌5秒","破魅","尖叫","尸化","技能复制","隐匿之雾","地狱之刃","地狱之刃","最后一搏","致盲","瞬移","狂暴","眩晕之锤","眩晕之锤","地狱之刃-眩晕","钢铁之躯","磁力场","磁力场-曲扭光环","磁力反击","沉默","清空目标子弹","减速","蝙蝠阵","巫尸之怒","巫尸之怒-吸血","毒爪","灵步","瞬秒1","缠绕","刚毅","践踏","铁拳","净化器","踩踏","无疆统治","无疆统治-吸附","无疆统治-闪电伤害"
         ,"群体狂暴","生命连结","反哺","反转术","七步毒","野性召唤","大地之怒","铁拳冲撞","灼热视线","狼图腾","疾风斩","遁地风暴","防弹外壳","防弹钢甲","防弹钢甲","绝命攻击","狂躁","喷射毒蛛","分身","狂暴","群体圣光","群体加速","瞬移","隐身","群体隐身","电离折射","爆石","闪电麻痹","定点轰炸","导弹召唤","万弹归宗","生命置换","沉默","全局溅射","嗜爪","毒爪","自燃","欺凌","馈赠","致残","减速","顽强","减速光环","致残光环","强击光环","腐蚀","荒芜光环","重生","复原","复原光环","反击","电离反转","瞬移","反馈--反弹被动技能","闪电麻痹-减速","生命置换-置换","技能复制-复制","全局溅射-链接","减速","只受到铁锹的攻击","只受到铁铲的攻击","只受圣诞礼炮攻击","不受任何元素伤害","血条等级显示倒计时","矿石产生物品","奉献","巨伤盾三","防御力提升20%","载具攻防","继续抵挡3次百分比伤害","减少无敌敌人伤害","靠近回血","主角抵挡3次百分比伤害","召唤毒蛛","反哺","反扑","电球爆发","永久隐身","酸雨","召唤毒蛛","增加血量","增加弹药","无敌药水","增加移动速度","召唤摩卡","无敌隐身-重生石","无敌隐身","月饼增加攻击力","汤圆增加攻击力","饺子无敌状态","双倍黑色武器掉落","双倍黑色装备掉落","双倍黑色武器掉落","双倍黑色装备掉落","神宠卡片","神车卡片","4倍载具伤害","磁力场","超级散射卡","骷髅卡","骷髅卡扣血","后来居上","夜空隐","狼震","金翼","龙翅","恶魔风脚","飞雷神","疾风斩","瞬秒2","小炎戒","英雄跳斩-降低目标攻击力","队长之魄","鼓舞士气","盛怒","无敌之怒","生化锁定","生化锁定X","动能推撞","超能推撞","挖掘者-动能推撞-推","技能衰减","竞技场-伤害加倍","击中减速","减速光环","主动加血","反弹伤害","怪物带着南关头","花火面具","沃龙面具","丛安面具","超级散射","无限馈赠","极限射速","战争狂人脱下头盔","丛林特种兵技能"
         ,"瞬秒3","攻击无视闪避","红外眼","无敌自爆僵尸攻击主角","狙击之王任务主角状态","狙击之王怪物攻击主角","狙击之王怪物受攻击","狙击之王怪物受攻击-技能","子弹碰撞","蝙蝠状态","步枪敏感","狙击敏感","散弹敏感","手枪敏感","火炮敏感","弩敏感","喷火器敏感","激光枪敏感","其他敏感","副手敏感","载具敏感","尸宠敏感","红武敏感","徒手敏感","巫尸之怒","猎击","酸性腐蚀","旋风刀","震地","狂刃追踪","远视","极速炮轰","补给头盔","弹性世界","全域圣光","聚能力量","飓风","巫尸之怒","牺牲","防御光环","上帝的护佑","无尽轰炸","聚能电流","退化光环","野性召唤","灼热射线","狼图腾","大地之怒","充能","辐射光球","静电过载","敏捷光环","致盲","减速","眩晕","灼热射线-附带技能","闪烁-目标点爆炸","自燃","狂暴","沉默","群体圣光","反击","电离折射","馈赠","先锋盾","电离反转","毒雾","定点轰炸","欺凌","闪烁","怒火","闪电麻痹","爆石","顽强","强击光环","复原光环","致残光环","减速光环","怪物猎人","精英克星","人类克星","恶魔猎手","恶魔屠刀","上帝之眼","上帝之杖","鬼步","上帝之眼-防御力降低","上帝之杖-防御力降低","闪避提升20%","防御力提升30%","主角攻击力提升10%","抵挡伤害","相同武器伤害叠加","攻击力提升30%","伤害光环150","雪藏","近战防御","千斤顶","千斤顶","白色血液效果","绿色血液效果","击中沉默","分身","风暴突袭","收到攻击打断风暴突袭","钩拉","钩雪莲华","钩拉-击中麻痹","钩雪莲华-击中","钩雪莲华-抛空中","腐蚀","飞雪连舞","雪引冲锋","爆怒一击","踢爆","召唤雪人","飞雪连舞","雪引冲锋-冲撞","踢爆-冲撞","飓风载体","诱击对玩家单位伤害降低","摩卡护体","敏捷馈赠","防御力提升","攻击力提升","真情之力","重生","回复","真情治愈","分子虹吸","分子虹吸-自身回血","荆棘外壳","血肉盛宴","雷霆斧击","野性冲锋","巨力滚"
         ,"上帝的护佑","免疫","磁力场","顽强光环","嗜爪之怒","瘴气","耐久光环","牺牲","钢背","负离子外壳","净化器","芒刺","荆棘外表","折射","召唤群狼","装甲压制","磁力场-曲扭光环","折射","嘲讽","遇强则刚","复仇","共鸣","暗夜信徒","恶爪","嘲讽敌人","反弹伤害","复仇伤害","击中减速","击毙补充弹药","击毙回复","剧毒","击毙溅射","振奋","击中溅射","致残","击中麻痹","致盲","击中回复","击中沉默","击中派生","瞬秒4","绝灭","快感","搏命","七步毒","爆石","引爆","刷新","击中闪电","击中眩晕","超级派生","超级麻痹","炎爆","冷凝","蚀骨","痛击","连弩","粘性","超级粘性","火焰始祖","贯穿波","无限火力","跳斩","上帝之杖","风雷","影灭","爆沙","爆胆","爆震","破甲","溃甲","飞镰","跟踪飞镰","战修罗","突袭","突袭","惩罚","子母弹","烟花","轨迹编辑","炎爆-爆","爆胆-无法叠加得buff","超级飞镰-破甲","轰天聚合","镇山聚合","霸空聚合","核弹头","核动力","冻血刀锋","守望之盾","合金外壳","遇强则强","冲击波","见面礼","降低攻击力","特效","特效","防御力提升","抵挡低于自身生命值的伤害。","抵挡4次百分比伤害","主角攻击力提升","攻击力提升40%","载具防御力","钢铁之躯","指定元素加成","掉率提升","狂战尸-震地减速","分身","狂刃爆发","召唤冥刃","分身","沉默","金钟雾","电离驱散","电离驱散-任何","加持","崩溃","凯撒特效附带","凯撒特效附带","石海buff","先锋盾","能量外壳","分身","副手防御","聚合防御","免疫","封锁","击落","受击坠落","统治圈","利刃盾","陨石雨","大地闪电","破宠","电离驱散","无敌驱散","超级散射","巨伤盾","尸毒","防空盾","激光盾","复仇之箭","复仇之魂","夺命箭","夺命魂","瞬秒所有除了载具","反击导弹","旋转电球","短命之仇","短命之仇-受到攻击","折寿","薄命","变尸"
         ,"死后杀死宿主","变蛛","竖盾","竖盾buff","强电","上帝之盾","锁血","瞬秒心零","监禁","禁无双","永久沉默","永久封锁","衰竭","群体圣光","狂暴","反击","派生导弹","吞噬","定点轰炸","远视","群体隐身","万弹归宗","金刚钻","毒雾","嗜爪","欺凌","魅惑","电离折射","馈赠","沉默","近视","先锋盾","全局溅射","群体自燃","精准","藐视","元素叠加","血盾","滑翔","翻滚","王者之翼","附身","全域光波","隐匿之雾","尖叫","反转术","全域圣光","妖魅","智慧怒火","毒雾-减血","吞噬-回血","反馈--反弹被动技能","自燃-链接","自燃-链接","翻滚-减速","沉默-使我方技能免疫3秒","击中眩晕","击中眩晕","清除燃气","燃气","原力俯冲","焦灼烈焰","巨石崩塌","硬甲","蛋护","海市蜃楼","饥荒","远古基因","解渴","巨焰围剿","月石","石化","矫捷姿态","召唤蝙蝠","龙甲","近战防御","石化眩晕","量子光束","末日轰炸","召唤生化罐","超导电幕","机械重组","超导电幕-第一阶段","超导电幕-第二阶段","超导电幕-怒气","超导电幕-防御","异角龙百分比伤害","召唤异角龙","死后清除所有召唤单位","电磁风暴","洲际导弹","洲际导弹-自爆","机械核心","钛钢甲","召唤机械核心","机械核心-死亡","机械核心-附着","机械核心-蓝闪电","机械核心-红闪电","机械核心-受伤","狼狈为奸","尸狼-死亡","无敌光环","磁力场","墙","强击光环","被击中麻痹","星际尘埃","星辰buff","解析射线","虚幻镜像","召唤随从","反哺","反物质","击中眩晕","击中死亡","恶魔火","自燃-链接","驾驭星火","击中麻痹","墟洞岩火","无名火","稀有元素","孪生之力","无法释放墟洞岩火buff","打断墟洞岩火","对玩家造成伤害","对分身造成伤害","使自身恐惧","使首领恐惧","水卷风","水汽溅腾","地精","融弹回血","反转气泡","眩晕","转变颜色","死亡","蓝","绿","弹跳","泰山压顶","蝎舞风暴"
         ,"战栗光","潮汐护甲","阳风增加攻击力","毒舌钩","炸裂者-舌头钩-击中","舌头钩-击中后移动则受到伤害","布雷风暴","地雷陷阱","暗影突袭","静电场","静电场-吸附","致命打击","超级弹幕","极速伤害","瘟疫","织网","织网-隐身","织网-回血","繁殖","复仇之刺","复仇之刺-击中被拖行","决斗者-特效预处理","旋风斩","旋风斩-减速","旋风波","决斗术","决斗术-击晕目标","分身术","召唤古惑"];
         var ArmsNow:* = null;
         ArrNum = str0.split("&",str0.length);
         s0.inData_byObj(this.nowData.save);
         for each(ArmsNow in ArrNum)
         {
            ArrNow = ArmsNow.split("*",ArmsNow.length);
            if(ArrNow[0] == "24")
            {
               System.setClipboard(s0.armsImgLabel);
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("复制模型代码","#fd397b") + "成功！");
            }
            else if(ArrNow[0] == "25")
            {
               s0.shootSoundUrl = Number(Number(Number(Number(Number(Number(ArrNow[1]))))));
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器枪声为","#fd397b") + Number(Number(Number(Number(Number(Number(ArrNow[1])))))) + "成功！");
            }
            else if(ArrNow[0] == "26")
            {
               if(ArrNow[1] == "*全部添加所有技能" || ArrNow[1] == "全部添加所有技能")
               {
                  s0.skillArr = ArrColor0.concat([]);
                  Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("添加所有技能","#fd397b") + "成功！共添加" + ArrColor0.length + "个技能！");
               }
               else if(ArrNow[1] == "清空" || ArrNow[1] == "*清空" || ArrNow[1] == "无" || ArrNow[1] == "*无")
               {
                  s0.skillArr = [];
                  Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("清空武器技能","#fd397b") + "成功！");
               }
               else
               {
                  i = 0;
                  while(i < ArrColor0.length)
                  {
                     if(ArrNow[1] == ArrColor1[i])
                     {
                        s0.skillArr = [ArrColor0[i]];
                        Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置武器技能","#fd397b") + "成功！");
                        break;
                     }
                     i++;
                  }
                  if(i >= ArrColor0.length)
                  {
                     Gaming.uiGroup.alertBox.showError("未找到技能：" + ArrNow[1]);
                  }
               }
            }
         }
         this.nowData.save = s0;
         // 标记武器为已修改状态
         this.nowData.save.lockB = true;
         // 添加兼容性标记，防止其他版本重置数据
         this.nowData.save.customEditB = true;
         // 刷新武器数据以确保修改生效
         if(this.nowData.placeType == "bag")
         {
            this.nowData.fleshData_byEquip(Gaming.PG.DATA.equip.mergeData);
         }
         else
         {
            this.nowData.fleshData_byMeEquip();
         }
         Gaming.uiGroup.allBagUI.fleshAllBox();
         this.showOneArmsDataAndPan(this.nowData);
      }
      
      private function affterGotoBack() : void
      {
         this.remakeBySave(this.beforeSave);
         this.beforeSave = null;
         Gaming.soundGroup.playSound("uiSound","getItems");
      }
      
      private function affterGetStoreState(v0:int) : void
      {
         var must_d0:MustDefine = null;
         if(v0 == 1 || v0 == -2)
         {
            Gaming.uiGroup.connectUI.hide();
            must_d0 = ArmsRemakeCtrl.getMust(this.nowData.save,this.nowProDataArr);
            PlayerMustCtrl.deductMust(must_d0,this.afterRemake);
         }
      }
      
      private function remakeBySave(s0:ArmsSave) : void
      {
         ArmsRemakeCtrl.setAllByOther(this.nowData.save,s0);
         if(this.nowData.placeType == "bag")
         {
            this.nowData.fleshData_byEquip(Gaming.PG.DATA.equip.mergeData);
         }
         else
         {
            this.nowData.fleshData_byMeEquip();
         }
         var nowProDataArr2:Array = ArmsSpecialAndSkill.getOneProDataArr(this.nowData.save);
         OneProData.setLockByOther(nowProDataArr2,this.nowProDataArr);
         this.nowProDataArr = nowProDataArr2;
         this.showOneArmsDataAndPan(this.nowData,false);
         Gaming.uiGroup.allBagUI.fleshAllBox();
      }
      
      private function afterRemake() : void
      {
         this.beforeSave = new ArmsSave();
         this.beforeSave.inData_byObj(this.nowData.save);
         this.remakeBySave(this.tempRemakeSave);
         Gaming.PG.save.headCount.armsRemakeNum++;
         UIOrder.save(true,true,true);
      }
   }
}

