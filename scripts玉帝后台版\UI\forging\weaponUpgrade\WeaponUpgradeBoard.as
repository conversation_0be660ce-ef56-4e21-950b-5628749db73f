package UI.forging.weaponUpgrade
{
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.must.NormalMustBox;
   import UI.base.tip.TextGatherAnalyze;
   import com.sounto.oldUtils.ComMethod;
   import dataAll.equip.EquipDataGroup;
   import dataAll.equip.weapon.WeaponData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.external.ExternalInterface;
   import flash.text.TextField;
   
   public class WeaponUpgradeBoard extends NormalUI
   {
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var beforeGrip:ItemsGrid = new ItemsGrid();
      
      private var nextGrip:ItemsGrid = new ItemsGrid();
      
      private var mustBox:NormalMustBox = new NormalMustBox();
      
      public var nowData:WeaponData = null;

      public var Aa:String;

      public var Bb:int;
      
      private var mustBoxSp:Sprite;
      
      private var btnSp:MovieClip;
      
      private var beforeTag:Sprite;
      
      private var nextTag:Sprite;
      
      private var beforeTxt:TextField;
      
      private var nextTxt:TextField;
      
      public function WeaponUpgradeBoard()
      {
         ExternalInterface.addCallback("weapon_EditCheating",Weapon_EditCheating);
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["mustBoxSp","btnSp","beforeTag","nextTag","beforeTxt","nextTxt"];
         super.setImg(img0);
         addChild(this.btn);
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         this.beforeTag.addChild(this.beforeGrip);
         this.beforeGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.beforeGrip);
         this.nextTag.addChild(this.nextGrip);
         this.nextGrip.setImgToEquipGrip();
         ItemsGripTipCtrl.addEvent_byItemsGrip(this.nextGrip);
         addChild(this.mustBox);
         this.mustBox.setImg(this.mustBoxSp);
         this.btn.setImg(this.btnSp);
         this.btn.setName("副手编辑");
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.allBagUI.chooseItemsData_out(this.nowData,"weapon");
         this.showOneWeaponDataAndPan(this.nowData);
      }
      
      public function outLoginEvent() : void
      {
         this.showNone();
      }
      
      public function equipGripClick(da0:WeaponData) : void
      {
         if(visible)
         {
            this.showOneWeaponDataAndPan(da0);
         }
      }
      
      private function showOneWeaponDataAndPan(da0:WeaponData) : void
      {
         var dg0:EquipDataGroup = null;
         this.showNone();
         Gaming.uiGroup.forgingUI.setCoverText("请在右边选择要进阶的副手。");
         if(Boolean(da0))
         {
            dg0 = Gaming.PG.da.findEquipData(da0);
            if(dg0 is EquipDataGroup)
            {
               this.showOneWeaponData(da0);
            }
            Gaming.uiGroup.forgingUI.setCoverText("");
         }
      }
      
      private function showOneWeaponData(da0:WeaponData) : void
      {
         var must_d0:MustDefine = null;
         var bb0:Boolean = false;
         this.nowData = da0;
         var afterData:WeaponData = da0.getUpradeData();
         this.inOneData(this.beforeGrip,this.beforeTxt,da0);
         this.inOneData(this.nextGrip,this.nextTxt,afterData);
         if(Boolean(afterData))
         {
            must_d0 = da0.getUpradeMust();
            bb0 = this.mustBox.inData(must_d0);
            this.btn.actived = bb0;
         }
         else
         {
            this.mustBox.setShowState(false);
            this.btn.actived = false;
         }
      }
      
      private function inOneData(grip0:ItemsGrid, txt0:TextField, da0:WeaponData) : void
      {
         var str0:String = "";
         if(!da0)
         {
            grip0.visible = false;
            str0 = "<green 已升至最高等级/>";
         }
         else
         {
            grip0.visible = true;
            grip0.inData_equip(da0);
            grip0.setNumText("");
            str0 = da0.getUIUpgradeGatherStr();
         }
         str0 = TextGatherAnalyze.swapText(str0);
         txt0.htmlText = str0;
      }
      
      private function showNone() : void
      {
         this.beforeGrip.clearData();
         this.nextGrip.clearData();
         this.beforeTxt.text = "";
         this.nextTxt.text = "";
         this.mustBox.setShowState(false);
         this.btn.actived = false;
      }
      
      public function btnClick(e:MouseEvent) : void
      {
         if(Boolean(this.nowData))
         {
            // 直接打开编辑界面，不需要检查升级条件
            this.afterEdit();
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("副手数据不存在！");
         }
      }
      
      private function afterEdit() : void
      {
         Gaming.uiGroup.alertBox.textInput.showTextInput("等级[00*数量] 强化[01*数量] 名称[02*文本]\n品质[03*英文] 耐久[04*数量]\n\n品质选项: white, green, blue, purple, orange, red\n","",this.Weapon_EditCheating);
      }

      public function Weapon_EditCheating(str0:String) : void
      {
         var TextArray:Array = new Array();
         TextArray = str0.split("*",str0.length);
         var name:String = String(TextArray[1]);
         this.Aa = TextArray[0];
         this.Bb = TextArray[1];

         if(Boolean(this.nowData))
         {
            if(this.Aa == "00" || this.Aa == "等级")
            {
               this.nowData.save.itemsLevel = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手等级" + TextArray[1]);
            }
            if(this.Aa == "01" || this.Aa == "强化")
            {
               this.nowData.save.strengthenLevel = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手强化等级" + TextArray[1]);
            }
            if(this.Aa == "02" || this.Aa == "名称")
            {
               this.nowData.save.playerName = name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手名称" + TextArray[1]);
            }
            if(this.Aa == "03" || this.Aa == "品质")
            {
               this.nowData.save.color = name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手品质:" + TextArray[1]);
            }
            if(this.Aa == "04" || this.Aa == "耐久")
            {
               this.nowData.save.durability = this.Bb;
               Gaming.uiGroup.alertBox.showSuccess("设置当前副手耐久" + TextArray[1]);
            }
         }
      }
   }
}

